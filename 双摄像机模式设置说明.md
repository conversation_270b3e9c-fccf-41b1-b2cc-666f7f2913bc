# 双摄像机模式设置说明

## 概述
现在系统已更新为双摄像机模式，可以同时显示第一人称和第三人称视角，无需切换。主视图显示第一人称视角，右上角显示第三人称视角的小窗口。

## 功能特点

### 双视角同时显示
- **主视图**：第一人称摄像机占据全屏，提供沉浸式体验
- **小窗口**：第三人称摄像机显示在右上角，提供外部观察视角
- **无需切换**：两个视角同时工作，实时更新

### 可调节的小窗口
- 窗口大小：默认为屏幕的30%×30%
- 窗口位置：默认在右上角（70%, 70%）
- 可通过代码调整大小和位置

### 视觉增强
- 第三人称小窗口带有白色边框
- 窗口上方显示"第三人称视角"标签
- 半透明背景提高可读性

## 控制说明

### 移动控制
- **W/S** 或 **↑/↓** - 前后移动
- **A/D** 或 **←/→** - 左右旋转

### 摄像机控制
- **T键** - 切换第三人称小窗口的显示/隐藏
- **H键** - 显示/隐藏控制说明

## 技术实现

### 摄像机设置
- **第一人称摄像机**：
  - Viewport Rect: (0, 0, 1, 1) - 全屏
  - Depth: 0 - 较低渲染深度

- **第三人称摄像机**：
  - Viewport Rect: (0.7, 0.7, 0.3, 0.3) - 右上角小窗口
  - Depth: 1 - 较高渲染深度，显示在上层

### 脚本更新

#### CameraManager.cs
- 移除了切换逻辑，改为双摄像机同时工作
- 新增第三人称窗口的显示/隐藏控制
- 支持动态调整窗口大小和位置

#### CameraRigThirdPerson.cs
- 移除了enabled状态控制
- 第三人称摄像机始终保持活动状态

#### ThirdPersonWindowBorder.cs（新增）
- 为第三人称小窗口绘制边框
- 显示标签文字
- 可自定义边框颜色和样式

#### ControlsUI.cs
- 更新控制说明以反映新的双摄像机模式
- 调整UI布局以容纳更多信息

## 自定义选项

### 调整窗口大小和位置
在CameraManager脚本中可以调整以下参数：
- `thirdPersonWindowSize`: 窗口大小（屏幕比例）
- `thirdPersonWindowPosition`: 窗口位置（屏幕比例）

### 修改边框样式
在ThirdPersonWindowBorder脚本中可以调整：
- `borderColor`: 边框颜色
- `borderWidth`: 边框宽度
- `labelText`: 标签文字
- `labelColor`: 标签颜色

### 更改控制按键
在CameraManager脚本中修改：
- `toggleThirdPersonKey`: 切换第三人称窗口的按键

## 使用方法

### 自动设置（推荐）
1. 在Hierarchy中创建空GameObject，命名为"CameraSetupHelper"
2. 添加CameraSetupHelper脚本
3. 在Inspector中点击"自动设置第三人称摄像机系统"按钮
4. 系统会自动创建所有必要的组件

### 手动设置
如果需要手动设置，确保：
1. UserMarker下有CameraRig结构
2. CameraRig中有Main Camera（第一人称）
3. 添加ThirdPersonCamera到CameraRig中
4. 在UserMarker上添加CameraManager脚本
5. 正确分配所有摄像机引用

## 优势

### 用户体验
- 同时观察两个视角，更好地理解物体运动
- 无需频繁切换视角
- 第一人称提供沉浸感，第三人称提供空间感

### 技术优势
- 利用Unity的多摄像机渲染系统
- 高效的Viewport分割
- 独立的摄像机深度控制

## 故障排除

### 如果第三人称窗口不显示
1. 检查第三人称摄像机是否正确分配
2. 确认摄像机的enabled状态
3. 检查Viewport Rect设置

### 如果边框不显示
1. 确认ThirdPersonWindowBorder脚本已添加
2. 检查showBorder选项是否开启
3. 确认CameraManager引用正确

### 如果窗口位置不正确
1. 调整thirdPersonWindowPosition参数
2. 注意Unity GUI坐标系（左上角为原点）
3. 确保位置值在0-1范围内

## 扩展建议

1. **多窗口支持**：可以添加更多小窗口显示不同角度
2. **窗口拖拽**：实现运行时拖拽调整窗口位置
3. **预设位置**：提供多个预设的窗口位置选项
4. **录制功能**：分别录制不同视角的视频
5. **画中画效果**：添加更多视觉效果如阴影、圆角等
