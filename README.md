# 机场导航演示项目

这是一个基于Unity开发的机场导航演示项目，用于模拟和可视化机场内的路径导航和位置跟踪。

## 项目概述

本项目是一个Unity 3D应用程序，主要功能包括：

1. 根据CSV文件中的路径数据进行路径跟踪和可视化
2. 通过网络接收位置数据并更新物体位置
3. 支持用户通过键盘控制物体移动

## 文件结构

```
AirportNavigationDemo/
├── Assets/                      # Unity资源文件夹
│   ├── Resource/                # 资源文件
│   │   └── PATH 1.csv           # 路径数据文件
│   ├── Scenes/                  # 场景文件
│   │   └── SampleScene.unity    # 示例场景
│   └── Scripts/                 # 脚本文件
│       ├── CsvPathFollower.cs   # CSV路径跟踪脚本
│       ├── NetworkReceiver.cs   # 网络接收器脚本
│       └── UserMovement.cs      # 用户移动控制脚本
├── ProjectSettings/             # Unity项目设置
├── Packages/                    # Unity包管理
└── AirportNavigationDemo.sln    # 解决方案文件
```

## 主要脚本功能

### CsvPathFollower.cs

这个脚本负责读取CSV文件中的路径数据，并控制物体沿着指定路径移动。主要功能包括：

- 从CSV文件加载路径点
- 将物体沿路径平滑移动
- 使用LineRenderer组件可视化路径轨迹
- 支持坐标系转换（从CSV坐标到Unity坐标）

### NetworkReceiver.cs

这个脚本实现了UDP网络通信，可以接收外部发送的位置数据，并更新场景中物体的位置和旋转。主要功能包括：

- 监听UDP端口（默认12345）接收数据
- 解析JSON格式的位置数据
- 实时更新物体的位置和旋转角度

### UserMovement.cs

这个脚本允许用户通过键盘控制物体移动。主要功能包括：

- 使用W/S或上/下方向键控制前后移动
- 使用A/D或左/右方向键控制旋转
- 可配置移动速度和旋转速度

## 路径数据格式

项目使用CSV格式的路径数据文件，格式如下：

```
Time,X,Y,Theta (degrees),Linear Velocity,Angular Velocity
0,0,0,90,-0.5,0
1,-3.06E-17,-0.5,90,-0.5,0
...
```

每行包含以下字段：
- Time：时间戳
- X, Y：位置坐标
- Theta：方向角（度）
- Linear Velocity：线性速度
- Angular Velocity：角速度

## 如何使用

### 运行项目

1. 使用Unity Hub打开项目文件夹
2. 打开Assets/Scenes/SampleScene.unity场景
3. 点击Unity编辑器中的播放按钮运行项目

### 控制物体移动

- 使用W/S或上/下方向键控制前后移动
- 使用A/D或左/右方向键控制旋转

### 修改路径数据

1. 可以编辑Assets/Resource/PATH 1.csv文件来修改路径数据
2. 也可以添加新的CSV文件，并在Inspector中将其分配给CsvPathFollower组件

### 接收外部位置数据

要从外部应用程序发送位置数据到本项目：

1. 确保外部应用程序发送UDP数据包到本机的12345端口
2. 数据格式应为JSON，例如：`{"x": 10.0, "z": 5.0, "angle": 90.0}`

## 配置参数

### CsvPathFollower组件

- csvFile：CSV路径数据文件
- targetObject：要移动的目标物体
- moveSpeed：移动速度
- samplingStep：采样步长（每隔多少行采样一个点）

### NetworkReceiver组件

- userArrow：接收位置更新的目标物体

### UserMovement组件

- moveSpeed：移动速度
- rotationSpeed：旋转速度

## 注意事项

- CSV文件中的坐标系与Unity坐标系不同，需要通过ConvertToUnity函数进行转换
- 网络接收功能需要确保防火墙允许UDP端口12345的通信
- 项目中的中文注释可能在某些编辑器中显示为乱码，请使用支持UTF-8编码的编辑器打开

## 系统要求

- Unity 2022.3.54f1或更高版本
- 支持.NET Framework的开发环境（如Visual Studio）

## 扩展功能

本项目可以进一步扩展以实现更多功能，例如：

- 添加更复杂的路径规划算法
- 实现多物体路径跟踪
- 集成实际机场地图和3D模型
- 添加用户界面进行交互控制
- 实现与实际定位系统的集成
