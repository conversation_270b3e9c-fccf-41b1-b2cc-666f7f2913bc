using UnityEngine;

public class CameraRigThirdPerson : MonoBehaviour
{
    [Header("目标设置")]
    public Transform target;  // 要跟随的目标物体（UserMarker）

    [Header("摄像机位置设置")]
    public bool useCurrentPositionAsOffset = true;  // 使用当前摄像机位置作为偏移
    public Vector3 offset = new Vector3(0, 5f, -10f);  // 相对于目标的位置偏移（当useCurrentPositionAsOffset为false时使用）
    public bool smoothFollow = true;  // 是否平滑跟随
    public float smoothSpeed = 5f;  // 平滑跟随速度

    [Header("摄像机朝向设置")]
    public bool lookAtTarget = true;  // 是否始终看向目标
    public Vector3 lookAtOffset = Vector3.zero;  // 看向目标的偏移

    private Camera thisCamera;  // 这个摄像机组件
    private Vector3 initialOffset;  // 初始偏移（从摄像机当前位置计算）
    private bool offsetCalculated = false;  // 是否已计算偏移

    void Start()
    {
        thisCamera = GetComponent<Camera>();

        // 如果没有指定目标，尝试找到带有ArrowMovement组件的物体
        if (target == null)
        {
            ArrowMovement arrow = FindObjectOfType<ArrowMovement>();
            if (arrow != null)
            {
                target = arrow.transform;
            }
        }

        // 确保摄像机初始状态是禁用的（由CameraManager控制）
        if (thisCamera != null)
        {
            thisCamera.enabled = false;
        }
    }

    void LateUpdate()
    {
        // 只有在摄像机启用时才更新
        if (thisCamera == null || !thisCamera.enabled || target == null) return;

        // 计算偏移（如果使用当前位置作为偏移且还未计算）
        if (useCurrentPositionAsOffset && !offsetCalculated)
        {
            initialOffset = transform.position - target.position;
            offsetCalculated = true;
        }

        // 确定使用的偏移
        Vector3 currentOffset = useCurrentPositionAsOffset ? initialOffset : offset;

        // 计算目标位置
        Vector3 targetPosition = target.position + currentOffset;

        // 移动摄像机到目标位置
        if (smoothFollow)
        {
            // 平滑跟随
            transform.position = Vector3.Lerp(transform.position, targetPosition, smoothSpeed * Time.deltaTime);
        }
        else
        {
            // 直接跟随
            transform.position = targetPosition;
        }

        // 设置摄像机朝向
        if (lookAtTarget)
        {
            Vector3 lookAtPosition = target.position + lookAtOffset;
            transform.LookAt(lookAtPosition);
        }
    }

    // 重置摄像机到默认位置
    public void ResetCamera()
    {
        // 简单版本不需要特殊的重置逻辑
        // 摄像机会自动跟随到正确位置
    }

    // 设置新的跟随目标
    public void SetTarget(Transform newTarget)
    {
        target = newTarget;
    }

    // 启用/禁用摄像机
    public void SetCameraEnabled(bool enabled)
    {
        if (thisCamera != null)
        {
            thisCamera.enabled = enabled;
        }
    }

    // 设置摄像机偏移位置
    public void SetOffset(Vector3 newOffset)
    {
        offset = newOffset;
        useCurrentPositionAsOffset = false;  // 切换到手动偏移模式
    }

    // 重新计算当前位置作为偏移
    public void RecalculateOffsetFromCurrentPosition()
    {
        if (target != null)
        {
            initialOffset = transform.position - target.position;
            offsetCalculated = true;
            useCurrentPositionAsOffset = true;
        }
    }

    // 切换偏移模式
    public void SetUseCurrentPositionAsOffset(bool useCurrentPosition)
    {
        useCurrentPositionAsOffset = useCurrentPosition;
        if (useCurrentPosition)
        {
            offsetCalculated = false;  // 重新计算偏移
        }
    }
}
