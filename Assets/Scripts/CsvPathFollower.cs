﻿using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using System.IO;
//目前已经实现了根据csv移动，移动有路径绘制，但不不知道是因为地图比例不对，还是因为2d和3d的点的转换公式没对应上，导致移动时起始点和终点对应不上（可能需要再看看这个转换公式）
public class CsvPathFollower : MonoBehaviour
{
    public TextAsset csvFile;
    public GameObject targetObject;
    public float moveSpeed = 5;
    public int samplingStep = 5;  // 每隔几行采样一个点，默认每10行采一个
    public float heightValue = 9.5f; // 添加高度参数，可在Inspector中调整
    private List<Vector3> pathPoints = new List<Vector3>();
    private LineRenderer lineRenderer;

    void Start()
    {
        lineRenderer = targetObject.GetComponent<LineRenderer>();
        if (lineRenderer == null)
        {
            lineRenderer = targetObject.AddComponent<LineRenderer>();
        }

        lineRenderer.positionCount = 0;
        lineRenderer.startWidth = 0.05f;
        lineRenderer.endWidth = 0.05f;
        lineRenderer.material = new Material(Shader.Find("Sprites/Default"));
        lineRenderer.startColor = Color.red;
        lineRenderer.endColor = Color.red;

        LoadCsvPath();

        // 将物体直接放到 CSV 的第一个位置
        if (pathPoints.Count > 0)
        {
            targetObject.transform.position = pathPoints[0];
        }

        StartCoroutine(FollowPath());
    }

    void LoadCsvPath()
    {
        using (StringReader reader = new StringReader(csvFile.text))
        {
            string line;
            bool skipHeader = true;
            int lineIndex = 0;

            while ((line = reader.ReadLine()) != null)
            {
                if (skipHeader) { skipHeader = false; continue; }

                // 采样控制：每隔 samplingStep 行取一个点
                if (lineIndex % samplingStep != 0)
                {
                    lineIndex++;
                    continue;
                }
                lineIndex++;

                string[] parts = line.Split(',');
                if (parts.Length >= 3)
                {
                    float realX = float.Parse(parts[1]);
                    float realY = float.Parse(parts[2]);
                    Vector3 unityPos = ConvertToUnity(realX, realY);
                    pathPoints.Add(unityPos);
                }
            }
        }


    }


    IEnumerator FollowPath()
    {
        for (int i = 0; i < pathPoints.Count - 1; i++)
        {
            Vector3 start = pathPoints[i];
            Vector3 end = pathPoints[i + 1];
            float distance = Vector3.Distance(start, end);
            float travelTime = distance / moveSpeed;

            float elapsedTime = 0f;
            while (elapsedTime < travelTime)
            {
                elapsedTime += Time.deltaTime;
                float t = Mathf.Clamp01(elapsedTime / travelTime);
                Vector3 pos = Vector3.Lerp(start, end, t);
                targetObject.transform.position = pos;

                // 画轨迹
                lineRenderer.positionCount++;
                lineRenderer.SetPosition(lineRenderer.positionCount - 1, pos);

                yield return null;
            }
        }
    }


    // 坐标转换函数：从 CSV (x, y) 映射到 Unity (x, z)
    Vector3 ConvertToUnity(float x, float y)
    {
        // 若已有统一比例/旋转，可在这里扩展
        return new Vector3(x, heightValue, -y);
    }
}
