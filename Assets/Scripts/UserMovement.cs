using UnityEngine;

public class ArrowMovement : MonoBehaviour
{
    public float moveSpeed = 0.05f;  // 增加移动速度以便更好观察
    public float rotationSpeed = 50f;

    void Update()
    {
        // 前后移动
        float move = Input.GetAxis("Vertical");   // W/S or 上/下
        transform.position += transform.forward * move * moveSpeed * Time.deltaTime;

        // 左右旋转
        float turn = Input.GetAxis("Horizontal"); // A/D or 左/右
        transform.Rotate(0, turn * rotationSpeed * Time.deltaTime, 0);
    }
}
