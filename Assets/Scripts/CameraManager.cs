using UnityEngine;

public class CameraManager : MonoBehaviour
{
    [Header("摄像机设置")]
    public Camera firstPersonCamera;  // 第一人称摄像机（CameraRig中的Main Camera）
    public Camera thirdPersonCamera;  // 第三人称摄像机（CameraRig中的新摄像机）

    [Header("CameraRig设置")]
    public Transform cameraRig;  // CameraRig Transform
    public Transform playerTransform;  // 玩家物体（UserMarker）

    [Header("第一人称设置")]
    public bool useOriginalFirstPersonSetup = true;  // 是否使用原有的第一人称设置
    public Vector3 firstPersonOffset = new Vector3(0, 1.5f, 0);  // 第一人称摄像机偏移（仅在不使用原有设置时生效）

    [Header("控制设置")]
    public KeyCode switchCameraKey = KeyCode.C;  // 切换摄像机的按键

    private bool isFirstPerson = true;  // 当前是否为第一人称视角
    private CameraRigThirdPerson thirdPersonScript;

    void Start()
    {
        // 自动查找CameraRig和玩家物体
        if (cameraRig == null)
        {
            cameraRig = GameObject.Find("CameraRig")?.transform;
        }

        if (playerTransform == null)
        {
            ArrowMovement arrow = FindObjectOfType<ArrowMovement>();
            if (arrow != null)
            {
                playerTransform = arrow.transform;
                // 如果没有指定CameraRig，尝试在玩家物体下查找
                if (cameraRig == null)
                {
                    cameraRig = playerTransform.Find("CameraRig");
                }
            }
        }

        // 自动查找摄像机
        if (firstPersonCamera == null && cameraRig != null)
        {
            firstPersonCamera = cameraRig.GetComponentInChildren<Camera>();
        }

        // 获取第三人称摄像机脚本
        if (thirdPersonCamera != null)
        {
            thirdPersonScript = thirdPersonCamera.GetComponent<CameraRigThirdPerson>();
        }

        // 设置第三人称摄像机的目标
        if (thirdPersonScript != null && playerTransform != null)
        {
            thirdPersonScript.SetTarget(playerTransform);
        }

        // 初始化摄像机状态
        SetCameraMode(isFirstPerson);
    }

    void Update()
    {
        // 检测切换摄像机按键
        if (Input.GetKeyDown(switchCameraKey))
        {
            SwitchCamera();
        }

        // 更新第一人称摄像机位置（仅在不使用原有设置时）
        if (isFirstPerson && !useOriginalFirstPersonSetup && firstPersonCamera != null && playerTransform != null)
        {
            UpdateFirstPersonCamera();
        }
    }

    void UpdateFirstPersonCamera()
    {
        if (cameraRig != null && playerTransform != null)
        {
            // 将CameraRig位置设置为玩家位置加上偏移
            cameraRig.position = playerTransform.position + firstPersonOffset;

            // 让CameraRig跟随玩家的旋转
            cameraRig.rotation = playerTransform.rotation;
        }
    }

    public void SwitchCamera()
    {
        isFirstPerson = !isFirstPerson;
        SetCameraMode(isFirstPerson);

        // 显示切换信息
        Debug.Log(isFirstPerson ? "切换到第一人称视角" : "切换到第三人称视角");
    }

    void SetCameraMode(bool firstPerson)
    {
        if (firstPersonCamera != null)
        {
            firstPersonCamera.enabled = firstPerson;
        }

        if (thirdPersonScript != null)
        {
            thirdPersonScript.SetCameraEnabled(!firstPerson);
        }

        // 如果切换到第三人称，重置摄像机位置
        if (!firstPerson && thirdPersonScript != null)
        {
            thirdPersonScript.ResetCamera();
        }
    }

    // 公共方法：设置为第一人称
    public void SetFirstPerson()
    {
        isFirstPerson = true;
        SetCameraMode(true);
    }

    // 公共方法：设置为第三人称
    public void SetThirdPerson()
    {
        isFirstPerson = false;
        SetCameraMode(false);
    }

    // 获取当前摄像机模式
    public bool IsFirstPerson()
    {
        return isFirstPerson;
    }
}
