using UnityEngine;

public class CameraManager : MonoBehaviour
{
    [Header("摄像机设置")]
    public Camera firstPersonCamera;  // 第一人称摄像机（CameraRig中的Main Camera）
    public Camera thirdPersonCamera;  // 第三人称摄像机（CameraRig中的新摄像机）

    [Header("CameraRig设置")]
    public Transform cameraRig;  // CameraRig Transform
    public Transform playerTransform;  // 玩家物体（UserMarker）

    [Header("第一人称设置")]
    public bool useOriginalFirstPersonSetup = true;  // 是否使用原有的第一人称设置
    public Vector3 firstPersonOffset = new Vector3(0, 1.5f, 0);  // 第一人称摄像机偏移（仅在不使用原有设置时生效）

    [Header("第三人称小窗口设置")]
    public Vector2 thirdPersonWindowSize = new Vector2(0.3f, 0.3f);  // 第三人称窗口大小（屏幕比例）
    public Vector2 thirdPersonWindowPosition = new Vector2(0.7f, 0.7f);  // 第三人称窗口位置（屏幕比例，右上角）
    public int thirdPersonCameraDepth = 1;  // 第三人称摄像机渲染深度（高于主摄像机）

    [Header("控制设置")]
    public KeyCode toggleThirdPersonKey = KeyCode.T;  // 切换第三人称窗口显示/隐藏的按键

    private CameraRigThirdPerson thirdPersonScript;
    private bool thirdPersonWindowVisible = true;  // 第三人称窗口是否可见

    void Start()
    {
        // 自动查找CameraRig和玩家物体
        if (cameraRig == null)
        {
            cameraRig = GameObject.Find("CameraRig")?.transform;
        }

        if (playerTransform == null)
        {
            ArrowMovement arrow = FindObjectOfType<ArrowMovement>();
            if (arrow != null)
            {
                playerTransform = arrow.transform;
                // 如果没有指定CameraRig，尝试在玩家物体下查找
                if (cameraRig == null)
                {
                    cameraRig = playerTransform.Find("CameraRig");
                }
            }
        }

        // 自动查找摄像机
        if (firstPersonCamera == null && cameraRig != null)
        {
            firstPersonCamera = cameraRig.GetComponentInChildren<Camera>();
        }

        // 获取第三人称摄像机脚本
        if (thirdPersonCamera != null)
        {
            thirdPersonScript = thirdPersonCamera.GetComponent<CameraRigThirdPerson>();
        }

        // 设置第三人称摄像机的目标
        if (thirdPersonScript != null && playerTransform != null)
        {
            thirdPersonScript.SetTarget(playerTransform);
        }

        // 初始化双摄像机模式
        SetupDualCameraMode();
    }

    void Update()
    {
        // 检测切换第三人称窗口显示/隐藏按键
        if (Input.GetKeyDown(toggleThirdPersonKey))
        {
            ToggleThirdPersonWindow();
        }

        // 更新第一人称摄像机位置（仅在不使用原有设置时）
        if (!useOriginalFirstPersonSetup && firstPersonCamera != null && playerTransform != null)
        {
            UpdateFirstPersonCamera();
        }
    }

    void UpdateFirstPersonCamera()
    {
        if (cameraRig != null && playerTransform != null)
        {
            // 将CameraRig位置设置为玩家位置加上偏移
            cameraRig.position = playerTransform.position + firstPersonOffset;

            // 让CameraRig跟随玩家的旋转
            cameraRig.rotation = playerTransform.rotation;
        }
    }

    // 设置双摄像机模式
    void SetupDualCameraMode()
    {
        // 设置主摄像机（第一人称）占据全屏
        if (firstPersonCamera != null)
        {
            firstPersonCamera.enabled = true;
            firstPersonCamera.rect = new Rect(0, 0, 1, 1);  // 全屏
            firstPersonCamera.depth = 0;  // 较低的渲染深度
        }

        // 设置第三人称摄像机为右下角小窗口
        if (thirdPersonCamera != null)
        {
            thirdPersonCamera.enabled = thirdPersonWindowVisible;
            thirdPersonCamera.rect = new Rect(thirdPersonWindowPosition.x, thirdPersonWindowPosition.y,
                                            thirdPersonWindowSize.x, thirdPersonWindowSize.y);
            thirdPersonCamera.depth = thirdPersonCameraDepth;  // 较高的渲染深度，显示在上层
        }

        Debug.Log("双摄像机模式已设置：主摄像机全屏，第三人称摄像机右上角小窗口");
    }

    // 切换第三人称窗口的显示/隐藏
    public void ToggleThirdPersonWindow()
    {
        thirdPersonWindowVisible = !thirdPersonWindowVisible;

        if (thirdPersonCamera != null)
        {
            thirdPersonCamera.enabled = thirdPersonWindowVisible;
        }

        Debug.Log(thirdPersonWindowVisible ? "显示第三人称窗口" : "隐藏第三人称窗口");
    }

    // 公共方法：设置第三人称窗口可见性
    public void SetThirdPersonWindowVisible(bool visible)
    {
        thirdPersonWindowVisible = visible;
        if (thirdPersonCamera != null)
        {
            thirdPersonCamera.enabled = visible;
        }
    }

    // 公共方法：调整第三人称窗口大小和位置
    public void SetThirdPersonWindowRect(Vector2 position, Vector2 size)
    {
        thirdPersonWindowPosition = position;
        thirdPersonWindowSize = size;

        if (thirdPersonCamera != null)
        {
            thirdPersonCamera.rect = new Rect(position.x, position.y, size.x, size.y);
        }
    }

    // 获取第三人称窗口是否可见
    public bool IsThirdPersonWindowVisible()
    {
        return thirdPersonWindowVisible;
    }
}
