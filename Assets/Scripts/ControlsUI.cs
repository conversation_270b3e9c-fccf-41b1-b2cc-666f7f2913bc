using UnityEngine;

public class ControlsUI : MonoBehaviour
{
    [Header("UI设置")]
    public bool showControls = true;
    public KeyCode toggleUIKey = KeyCode.H;  // 切换UI显示的按键

    private bool uiVisible = true;
    private GUIStyle labelStyle;

    void Start()
    {
        // 初始化GUI样式
        labelStyle = new GUIStyle();
        labelStyle.fontSize = 16;
        labelStyle.normal.textColor = Color.white;
        labelStyle.alignment = TextAnchor.UpperLeft;
    }

    void Update()
    {
        if (Input.GetKeyDown(toggleUIKey))
        {
            uiVisible = !uiVisible;
        }
    }

    void OnGUI()
    {
        if (!showControls || !uiVisible) return;

        // 创建一个半透明的背景
        GUI.color = new Color(0, 0, 0, 0.7f);
        GUI.Box(new Rect(10, 10, 320, 240), "");

        // 重置颜色
        GUI.color = Color.white;

        // 显示控制说明
        string controlsText = "控制说明:\n\n" +
                             "移动控制:\n" +
                             "W/S 或 ↑/↓ - 前后移动\n" +
                             "A/D 或 ←/→ - 左右旋转\n\n" +
                             "摄像机控制:\n" +
                             "T - 切换第三人称小窗口显示/隐藏\n\n" +
                             "视角说明:\n" +
                             "主视图：第一人称视角\n" +
                             "右上角：第三人称视角\n\n" +
                             "H - 隐藏/显示此帮助";

        GUI.Label(new Rect(20, 20, 300, 220), controlsText, labelStyle);
    }
}
