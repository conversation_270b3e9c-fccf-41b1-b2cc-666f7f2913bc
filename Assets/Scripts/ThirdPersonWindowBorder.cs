using UnityEngine;

public class ThirdPersonWindowBorder : MonoBehaviour
{
    [Header("边框设置")]
    public bool showBorder = true;
    public Color borderColor = Color.white;
    public float borderWidth = 2f;
    
    [Header("标签设置")]
    public bool showLabel = true;
    public string labelText = "第三人称视角";
    public Color labelColor = Color.white;
    public int labelFontSize = 14;
    
    private CameraManager cameraManager;
    private GUIStyle borderStyle;
    private GUIStyle labelStyle;
    
    void Start()
    {
        // 查找CameraManager
        cameraManager = FindObjectOfType<CameraManager>();
        
        // 初始化GUI样式
        InitializeStyles();
    }
    
    void InitializeStyles()
    {
        // 边框样式
        borderStyle = new GUIStyle();
        borderStyle.normal.background = CreateColorTexture(borderColor);
        
        // 标签样式
        labelStyle = new GUIStyle();
        labelStyle.fontSize = labelFontSize;
        labelStyle.normal.textColor = labelColor;
        labelStyle.alignment = TextAnchor.MiddleCenter;
        labelStyle.fontStyle = FontStyle.Bold;
    }
    
    void OnGUI()
    {
        if (!showBorder || cameraManager == null || !cameraManager.IsThirdPersonWindowVisible())
            return;
            
        // 获取第三人称窗口的屏幕坐标
        Vector2 windowPos = cameraManager.thirdPersonWindowPosition;
        Vector2 windowSize = cameraManager.thirdPersonWindowSize;
        
        // 转换为屏幕像素坐标
        float screenWidth = Screen.width;
        float screenHeight = Screen.height;
        
        float x = windowPos.x * screenWidth;
        float y = (1f - windowPos.y - windowSize.y) * screenHeight; // Unity GUI坐标系Y轴翻转
        float width = windowSize.x * screenWidth;
        float height = windowSize.y * screenHeight;
        
        // 绘制边框
        DrawBorder(x, y, width, height);
        
        // 绘制标签
        if (showLabel)
        {
            DrawLabel(x, y, width);
        }
    }
    
    void DrawBorder(float x, float y, float width, float height)
    {
        // 上边框
        GUI.Box(new Rect(x - borderWidth, y - borderWidth, width + 2 * borderWidth, borderWidth), "", borderStyle);
        
        // 下边框
        GUI.Box(new Rect(x - borderWidth, y + height, width + 2 * borderWidth, borderWidth), "", borderStyle);
        
        // 左边框
        GUI.Box(new Rect(x - borderWidth, y, borderWidth, height), "", borderStyle);
        
        // 右边框
        GUI.Box(new Rect(x + width, y, borderWidth, height), "", borderStyle);
    }
    
    void DrawLabel(float x, float y, float width)
    {
        // 在窗口上方绘制标签
        float labelHeight = 20f;
        float labelY = y - borderWidth - labelHeight - 2f;
        
        // 标签背景
        GUI.color = new Color(0, 0, 0, 0.7f);
        GUI.Box(new Rect(x, labelY, width, labelHeight), "");
        
        // 重置颜色并绘制文字
        GUI.color = Color.white;
        GUI.Label(new Rect(x, labelY, width, labelHeight), labelText, labelStyle);
    }
    
    // 创建纯色纹理
    Texture2D CreateColorTexture(Color color)
    {
        Texture2D texture = new Texture2D(1, 1);
        texture.SetPixel(0, 0, color);
        texture.Apply();
        return texture;
    }
    
    // 公共方法：设置边框可见性
    public void SetBorderVisible(bool visible)
    {
        showBorder = visible;
    }
    
    // 公共方法：设置边框颜色
    public void SetBorderColor(Color color)
    {
        borderColor = color;
        if (borderStyle != null)
        {
            borderStyle.normal.background = CreateColorTexture(color);
        }
    }
    
    // 公共方法：设置标签文本
    public void SetLabelText(string text)
    {
        labelText = text;
    }
}
