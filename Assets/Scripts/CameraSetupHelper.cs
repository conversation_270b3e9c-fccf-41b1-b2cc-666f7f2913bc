using UnityEngine;

#if UNITY_EDITOR
using UnityEditor;
#endif

public class CameraSetupHelper : MonoBehaviour
{
    [Header("自动设置")]
    [SerializeField] private bool autoSetup = false;

    [Header("手动设置")]
    public Transform userMarker;
    public Transform cameraRig;
    public Camera mainCamera;

    [Space]
    [Header("设置按钮")]
    public bool setupThirdPersonCamera = false;

    void Start()
    {
        if (autoSetup)
        {
            SetupCameraSystem();
        }
    }

    void Update()
    {
        if (setupThirdPersonCamera)
        {
            setupThirdPersonCamera = false;
            SetupCameraSystem();
        }
    }

    public void SetupCameraSystem()
    {
        Debug.Log("开始设置第三人称摄像机系统...");

        // 1. 查找UserMarker
        if (userMarker == null)
        {
            ArrowMovement arrow = FindObjectOfType<ArrowMovement>();
            if (arrow != null)
            {
                userMarker = arrow.transform;
                Debug.Log("找到UserMarker: " + userMarker.name);
            }
            else
            {
                Debug.LogError("未找到带有ArrowMovement脚本的物体！");
                return;
            }
        }

        // 2. 查找CameraRig
        if (cameraRig == null)
        {
            cameraRig = userMarker.Find("CameraRig");
            if (cameraRig == null)
            {
                Debug.LogError("在UserMarker下未找到CameraRig！");
                return;
            }
            Debug.Log("找到CameraRig: " + cameraRig.name);
        }

        // 3. 查找Main Camera
        if (mainCamera == null)
        {
            mainCamera = cameraRig.GetComponentInChildren<Camera>();
            if (mainCamera == null)
            {
                Debug.LogError("在CameraRig下未找到摄像机！");
                return;
            }
            Debug.Log("找到Main Camera: " + mainCamera.name);
        }

        // 4. 创建第三人称摄像机
        Camera thirdPersonCamera = CreateThirdPersonCamera();
        if (thirdPersonCamera == null)
        {
            Debug.LogError("创建第三人称摄像机失败！");
            return;
        }

        // 5. 设置CameraManager
        SetupCameraManager(thirdPersonCamera);

        // 6. 创建控制UI
        CreateControlsUI();

        // 7. 创建第三人称窗口边框
        CreateThirdPersonWindowBorder();

        Debug.Log("双摄像机系统设置完成！");
        Debug.Log("主视图显示第一人称视角，右上角显示第三人称视角");
        Debug.Log("按T键切换第三人称窗口显示/隐藏，按H键显示/隐藏控制说明");
    }

    Camera CreateThirdPersonCamera()
    {
        // 检查是否已经存在第三人称摄像机
        Transform existingThirdPerson = cameraRig.Find("ThirdPersonCamera");
        if (existingThirdPerson != null)
        {
            Camera existingCamera = existingThirdPerson.GetComponent<Camera>();
            if (existingCamera != null)
            {
                Debug.Log("第三人称摄像机已存在，跳过创建");
                return existingCamera;
            }
        }

        // 创建新的摄像机
        GameObject thirdPersonCameraObj = new GameObject("ThirdPersonCamera");
        thirdPersonCameraObj.transform.SetParent(cameraRig);
        thirdPersonCameraObj.transform.localPosition = Vector3.zero;
        thirdPersonCameraObj.transform.localRotation = Quaternion.identity;

        // 添加Camera组件
        Camera thirdPersonCamera = thirdPersonCameraObj.AddComponent<Camera>();

        // 复制Main Camera的设置
        thirdPersonCamera.fieldOfView = mainCamera.fieldOfView;
        thirdPersonCamera.nearClipPlane = mainCamera.nearClipPlane;
        thirdPersonCamera.farClipPlane = mainCamera.farClipPlane;
        thirdPersonCamera.enabled = true; // 在双摄像机模式下启用

        // 设置第三人称摄像机的初始位置（在目标后方上方）
        thirdPersonCameraObj.transform.position = userMarker.position + new Vector3(0, 5f, -10f);

        // 添加第三人称摄像机脚本
        CameraRigThirdPerson thirdPersonScript = thirdPersonCameraObj.AddComponent<CameraRigThirdPerson>();
        thirdPersonScript.target = userMarker;
        thirdPersonScript.SetUseCurrentPositionAsOffset(true);  // 使用当前位置作为偏移

        Debug.Log("创建第三人称摄像机: " + thirdPersonCameraObj.name);
        return thirdPersonCamera;
    }

    void SetupCameraManager(Camera thirdPersonCamera)
    {
        // 检查UserMarker上是否已有CameraManager
        CameraManager existingManager = userMarker.GetComponent<CameraManager>();
        if (existingManager != null)
        {
            // 更新现有的CameraManager
            existingManager.firstPersonCamera = mainCamera;
            existingManager.thirdPersonCamera = thirdPersonCamera;
            existingManager.cameraRig = cameraRig;
            existingManager.playerTransform = userMarker;
            existingManager.useOriginalFirstPersonSetup = true;  // 保持原有的第一人称设置
            Debug.Log("更新现有的CameraManager");
        }
        else
        {
            // 添加新的CameraManager
            CameraManager cameraManager = userMarker.gameObject.AddComponent<CameraManager>();
            cameraManager.firstPersonCamera = mainCamera;
            cameraManager.thirdPersonCamera = thirdPersonCamera;
            cameraManager.cameraRig = cameraRig;
            cameraManager.playerTransform = userMarker;
            cameraManager.useOriginalFirstPersonSetup = true;  // 保持原有的第一人称设置
            Debug.Log("添加新的CameraManager到UserMarker");
        }
    }

    void CreateControlsUI()
    {
        // 检查是否已存在ControlsUI
        ControlsUI existingUI = FindObjectOfType<ControlsUI>();
        if (existingUI != null)
        {
            Debug.Log("ControlsUI已存在，跳过创建");
            return;
        }

        // 创建ControlsUI
        GameObject controlsUIObj = new GameObject("ControlsUI");
        controlsUIObj.AddComponent<ControlsUI>();
        Debug.Log("创建ControlsUI");
    }

    void CreateThirdPersonWindowBorder()
    {
        // 检查是否已存在ThirdPersonWindowBorder
        ThirdPersonWindowBorder existingBorder = FindObjectOfType<ThirdPersonWindowBorder>();
        if (existingBorder != null)
        {
            Debug.Log("ThirdPersonWindowBorder已存在，跳过创建");
            return;
        }

        // 创建ThirdPersonWindowBorder
        GameObject borderObj = new GameObject("ThirdPersonWindowBorder");
        borderObj.AddComponent<ThirdPersonWindowBorder>();
        Debug.Log("创建ThirdPersonWindowBorder");
    }
}

#if UNITY_EDITOR
[CustomEditor(typeof(CameraSetupHelper))]
public class CameraSetupHelperEditor : Editor
{
    public override void OnInspectorGUI()
    {
        DrawDefaultInspector();

        CameraSetupHelper helper = (CameraSetupHelper)target;

        GUILayout.Space(10);

        if (GUILayout.Button("自动设置第三人称摄像机系统", GUILayout.Height(30)))
        {
            helper.SetupCameraSystem();
        }

        GUILayout.Space(5);

        EditorGUILayout.HelpBox(
            "点击按钮将自动：\n" +
            "1. 查找UserMarker和CameraRig\n" +
            "2. 创建第三人称摄像机\n" +
            "3. 设置CameraManager\n" +
            "4. 创建控制UI\n\n" +
            "确保场景中有带ArrowMovement脚本的UserMarker物体！",
            MessageType.Info);
    }
}
#endif
