fileFormatVersion: 2
guid: fd09957580ac4326916010f1f260975b
timeCreated: **********
licenseType: Pro
TextureImporter:
  fileIDToRecycleName:
    21300000: 0
    21300002: 1
    21300004: 2
    21300006: 3
    21300008: 4
    21300010: 5
    21300012: 6
    21300014: 7
    21300016: 8
    21300018: 9
  serializedVersion: 2
  mipmaps:
    mipMapMode: 0
    enableMipMap: 1
    linearTexture: 0
    correctGamma: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: .25
    normalMapFilter: 0
  isReadable: 0
  grayScaleToAlpha: 0
  generateCubemap: 0
  cubemapConvolution: 0
  cubemapConvolutionSteps: 7
  cubemapConvolutionExponent: 1.5
  seamlessCubemap: 0
  textureFormat: -1
  maxTextureSize: 512
  textureSettings:
    filterMode: -1
    aniso: 16
    mipBias: -1
    wrapMode: 1
  nPOTScale: 1
  lightmap: 0
  rGBM: 0
  compressionQuality: 50
  allowsAlphaSplitting: 0
  spriteMode: 0
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: .5, y: .5}
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spritePixelsToUnits: 100
  alphaIsTransparency: 0
  textureType: 0
  buildTargetSettings: []
  spriteSheet:
    sprites:
    - name: 0
      rect:
        serializedVersion: 2
        x: 0
        y: 384
        width: 128
        height: 128
      alignment: 0
      pivot: {x: .5, y: .5}
      border: {x: 0, y: 0, z: 0, w: 0}
    - name: 1
      rect:
        serializedVersion: 2
        x: 128
        y: 384
        width: 128
        height: 128
      alignment: 0
      pivot: {x: .5, y: .5}
      border: {x: 0, y: 0, z: 0, w: 0}
    - name: 2
      rect:
        serializedVersion: 2
        x: 256
        y: 384
        width: 128
        height: 128
      alignment: 0
      pivot: {x: .5, y: .5}
      border: {x: 0, y: 0, z: 0, w: 0}
    - name: 3
      rect:
        serializedVersion: 2
        x: 384
        y: 384
        width: 128
        height: 128
      alignment: 0
      pivot: {x: .5, y: .5}
      border: {x: 0, y: 0, z: 0, w: 0}
    - name: 4
      rect:
        serializedVersion: 2
        x: 0
        y: 256
        width: 128
        height: 128
      alignment: 0
      pivot: {x: .5, y: .5}
      border: {x: 0, y: 0, z: 0, w: 0}
    - name: 5
      rect:
        serializedVersion: 2
        x: 128
        y: 256
        width: 128
        height: 128
      alignment: 0
      pivot: {x: .5, y: .5}
      border: {x: 0, y: 0, z: 0, w: 0}
    - name: 6
      rect:
        serializedVersion: 2
        x: 256
        y: 256
        width: 128
        height: 128
      alignment: 0
      pivot: {x: .5, y: .5}
      border: {x: 0, y: 0, z: 0, w: 0}
    - name: 7
      rect:
        serializedVersion: 2
        x: 384
        y: 256
        width: 128
        height: 128
      alignment: 0
      pivot: {x: .5, y: .5}
      border: {x: 0, y: 0, z: 0, w: 0}
    - name: 8
      rect:
        serializedVersion: 2
        x: 0
        y: 128
        width: 128
        height: 128
      alignment: 0
      pivot: {x: .5, y: .5}
      border: {x: 0, y: 0, z: 0, w: 0}
    - name: 9
      rect:
        serializedVersion: 2
        x: 128
        y: 128
        width: 128
        height: 128
      alignment: 0
      pivot: {x: .5, y: .5}
      border: {x: 0, y: 0, z: 0, w: 0}
  spritePackingTag: 
  userData: 
  assetBundleName: 
  assetBundleVariant: 
