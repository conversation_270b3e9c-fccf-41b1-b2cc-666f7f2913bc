%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 6
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Unity SDF - HDRP LIT - Bloom
  m_Shader: {fileID: -6465566751694194690, guid: ca2ed216f98028c4dae6c5224a952b3c, type: 3}
  m_ShaderKeywords: _ALPHATEST_ON _DISABLE_SSR_TRANSPARENT _DOUBLESIDED_ON _ENABLE_FOG_ON_TRANSPARENT
    _SURFACE_TYPE_TRANSPARENT
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 1
  m_CustomRenderQueue: 3000
  stringTagMap:
    MotionVector: User
    RenderType: Transparent
  disabledShaderPasses:
  - TransparentDepthPrepass
  - TransparentDepthPostpass
  - TransparentBackface
  - RayTracingPrepass
  - MOTIONVECTORS
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Cube:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _FaceTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 5445961698780662350, guid: c9adb1f542a93ae489bacfeae21e8394, type: 2}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OutlineTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Floats:
    - _AddPrecomputedVelocity: 0
    - _AlphaCutoffEnable: 1
    - _AlphaDstBlend: 10
    - _AlphaSrcBlend: 1
    - _AlphaToMask: 0
    - _AlphaToMaskInspectorValue: 0
    - _Ambient: 0.393
    - _Bevel: 0.5
    - _BevelAmount: 0
    - _BevelClamp: 0
    - _BevelOffset: 0
    - _BevelRoundness: 0
    - _BevelType: 1
    - _BevelWidth: 0
    - _BlendMode: 0
    - _BumpFace: 0
    - _BumpOutline: 0
    - _ColorMask: 15
    - _CullMode: 0
    - _CullModeForward: 0
    - _DepthOffsetEnable: 0
    - _Diffuse: 0
    - _DoubleSidedEnable: 1
    - _DoubleSidedNormalMode: 0
    - _DstBlend: 10
    - _EnableBlendModePreserveSpecularLighting: 1
    - _EnableFogOnTransparent: 1
    - _FaceDilate: 0
    - _FaceUVSpeedX: 0
    - _FaceUVSpeedY: 0
    - _GlowInner: 0.05
    - _GlowOffset: 0
    - _GlowOuter: 0.05
    - _GlowPower: 0.75
    - _GradientScale: 10
    - _LightAngle: 3.1416
    - _MaskSoftnessX: 0
    - _MaskSoftnessY: 0
    - _OpaqueCullMode: 2
    - _OutlineMode: 0
    - _OutlineSoftness: 0
    - _OutlineUVSpeedX: 0
    - _OutlineUVSpeedY: 0
    - _OutlineWidth: 0
    - _PerspectiveFilter: 0.875
    - _RayTracing: 0
    - _ReceivesSSR: 1
    - _ReceivesSSRTransparent: 0
    - _Reflectivity: 5
    - _RefractionModel: 0
    - _RenderQueueType: 4
    - _RequireSplitLighting: 0
    - _ScaleRatioA: 0.9
    - _ScaleRatioB: 0.73125
    - _ScaleRatioC: 0.73125
    - _ScaleX: 1
    - _ScaleY: 1
    - _ShaderFlags: 0
    - _Sharpness: 0
    - _SpecularPower: 0
    - _SrcBlend: 1
    - _Stencil: 0
    - _StencilComp: 8
    - _StencilOp: 0
    - _StencilReadMask: 255
    - _StencilRef: 0
    - _StencilRefDepth: 0
    - _StencilRefDistortionVec: 4
    - _StencilRefGBuffer: 2
    - _StencilRefMV: 32
    - _StencilWriteMask: 6
    - _StencilWriteMaskDepth: 8
    - _StencilWriteMaskDistortionVec: 4
    - _StencilWriteMaskGBuffer: 14
    - _StencilWriteMaskMV: 40
    - _SupportDecals: 1
    - _SurfaceType: 1
    - _TextureHeight: 1024
    - _TextureWidth: 1024
    - _TransparentBackfaceEnable: 0
    - _TransparentCullMode: 2
    - _TransparentDepthPostpassEnable: 0
    - _TransparentDepthPrepassEnable: 0
    - _TransparentSortPriority: 0
    - _TransparentWritingMotionVec: 0
    - _TransparentZWrite: 0
    - _UnderlayDilate: 0
    - _UnderlayOffsetX: 0
    - _UnderlayOffsetY: 0
    - _UnderlaySoftness: 0
    - _UseShadowThreshold: 0
    - _VertexOffsetX: 0
    - _VertexOffsetY: 0
    - _WeightBold: 0.75
    - _WeightNormal: 0
    - _ZTestDepthEqualForOpaque: 4
    - _ZTestGBuffer: 3
    - _ZTestTransparent: 4
    - _ZWrite: 0
    m_Colors:
    - _ClipRect: {r: -32767, g: -32767, b: 32767, a: 32767}
    - _DoubleSidedConstants: {r: -1, g: -1, b: -1, a: 0}
    - _EmissionColor: {r: 1, g: 1, b: 1, a: 1}
    - _EnvMatrixRotation: {r: 0, g: 0, b: 0, a: 0}
    - _FaceColor: {r: 1, g: 1, b: 1, a: 1}
    - _FaceText_ST: {r: 1, g: 1, b: 0, a: 0}
    - _FaceUVSpeed: {r: 0, g: 0, b: 0, a: 0}
    - _GlowColor: {r: 0, g: 1, b: 0, a: 0.5}
    - _IsoPerimeter: {r: 0, g: 0.3, b: 0.4, a: 0}
    - _MaskCoord: {r: 0, g: 0, b: 32767, a: 32767}
    - _OutlineColor: {r: 0, g: 0, b: 0, a: 1}
    - _OutlineColor1: {r: 0, g: 0, b: 0, a: 0}
    - _OutlineColor2: {r: 0, g: 28.109804, b: 95.87451, a: 1}
    - _OutlineColor3: {r: 0, g: 0, b: 0, a: 1}
    - _OutlineOffset1: {r: 0, g: 0, b: 0, a: 0}
    - _OutlineOffset2: {r: 0, g: 0, b: 0, a: 0}
    - _OutlineOffset3: {r: 0, g: 0, b: 0, a: 0}
    - _OutlineUVSpeed: {r: 0, g: 0, b: 0, a: 0}
    - _ReflectFaceColor: {r: 0, g: 0, b: 0, a: 1}
    - _ReflectOutlineColor: {r: 0, g: 0, b: 0, a: 1}
    - _Softness: {r: 0, g: 0, b: 0, a: 0}
    - _SpecularColor: {r: 1, g: 1, b: 1, a: 1}
    - _UnderlayColor: {r: 0, g: 0, b: 0, a: 0.5}
    - _UnderlayOffset: {r: 0, g: 0, b: 0, a: 0}
  m_BuildTextureStacks: []
--- !u!114 &5076635838026210647
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: da692e001514ec24dbc4cca1949ff7e8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 11
