%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 6
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Unity SDF - HDRP LIT - Outline
  m_Shader: {fileID: -6465566751694194690, guid: ca2ed216f98028c4dae6c5224a952b3c, type: 3}
  m_ShaderKeywords: _ALPHATEST_ON _DISABLE_DECALS _DISABLE_SSR_TRANSPARENT _DOUBLESIDED_ON
    _ENABLE_FOG_ON_TRANSPARENT _SURFACE_TYPE_TRANSPARENT
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 1
  m_CustomRenderQueue: 3000
  stringTagMap:
    MotionVector: User
    RenderType: Transparent
  disabledShaderPasses:
  - TransparentDepthPrepass
  - TransparentDepthPostpass
  - TransparentBackface
  - RayTracingPrepass
  - MOTIONVECTORS
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _FaceTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 5445961698780662350, guid: c9adb1f542a93ae489bacfeae21e8394, type: 2}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OutlineTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Floats:
    - _AddPrecomputedVelocity: 0
    - _AlphaCutoffEnable: 1
    - _AlphaDstBlend: 10
    - _AlphaSrcBlend: 1
    - _AlphaToMask: 0
    - _AlphaToMaskInspectorValue: 0
    - _Ambient: 0
    - _BevelAmount: 0
    - _BevelClamp: 0
    - _BevelOffset: 0
    - _BevelRoundness: 0
    - _BevelType: 0
    - _BevelWidth: 0.5
    - _BlendMode: 0
    - _CullMode: 0
    - _CullModeForward: 0
    - _DepthOffsetEnable: 0
    - _Diffuse: 0
    - _DoubleSidedEnable: 1
    - _DoubleSidedNormalMode: 0
    - _DstBlend: 10
    - _EnableBlendModePreserveSpecularLighting: 1
    - _EnableFogOnTransparent: 1
    - _GradientScale: 10
    - _LightAngle: 0
    - _OpaqueCullMode: 2
    - _OutlineMode: 0
    - _RayTracing: 0
    - _ReceivesSSR: 1
    - _ReceivesSSRTransparent: 0
    - _Reflectivity: 5
    - _RefractionModel: 0
    - _RenderQueueType: 4
    - _RequireSplitLighting: 0
    - _SpecularPower: 0
    - _SrcBlend: 1
    - _StencilRef: 0
    - _StencilRefDepth: 0
    - _StencilRefDistortionVec: 4
    - _StencilRefGBuffer: 2
    - _StencilRefMV: 32
    - _StencilWriteMask: 6
    - _StencilWriteMaskDepth: 8
    - _StencilWriteMaskDistortionVec: 4
    - _StencilWriteMaskGBuffer: 14
    - _StencilWriteMaskMV: 40
    - _SupportDecals: 0
    - _SurfaceType: 1
    - _TransparentBackfaceEnable: 0
    - _TransparentCullMode: 2
    - _TransparentDepthPostpassEnable: 0
    - _TransparentDepthPrepassEnable: 0
    - _TransparentSortPriority: 0
    - _TransparentWritingMotionVec: 0
    - _TransparentZWrite: 0
    - _UnderlayDilate: 0
    - _UnderlaySoftness: 0
    - _UseShadowThreshold: 0
    - _ZTestDepthEqualForOpaque: 4
    - _ZTestGBuffer: 3
    - _ZTestTransparent: 4
    - _ZWrite: 0
    m_Colors:
    - _DoubleSidedConstants: {r: -1, g: -1, b: -1, a: 0}
    - _EmissionColor: {r: 1, g: 1, b: 1, a: 1}
    - _FaceColor: {r: 1, g: 1, b: 1, a: 1}
    - _FaceText_ST: {r: 1, g: 1, b: 0, a: 0}
    - _FaceUVSpeed: {r: 0, g: 0, b: 0, a: 0}
    - _IsoPerimeter: {r: 0, g: 0.188, b: 0, a: 0}
    - _OutlineColor1: {r: 0, g: 0, b: 0, a: 1}
    - _OutlineColor2: {r: 0.009433985, g: 0.02534519, b: 1, a: 1}
    - _OutlineColor3: {r: 0, g: 0, b: 0, a: 1}
    - _OutlineOffset1: {r: 0, g: 0, b: 0, a: 0}
    - _OutlineOffset2: {r: 0, g: 0, b: 0, a: 0}
    - _OutlineOffset3: {r: 0, g: 0, b: 0, a: 0}
    - _OutlineUVSpeed: {r: 0, g: 0, b: 0, a: 0}
    - _Softness: {r: 0, g: 0, b: 0, a: 0}
    - _SpecularColor: {r: 1, g: 1, b: 1, a: 1}
    - _UnderlayColor: {r: 0, g: 0, b: 0, a: 1}
    - _UnderlayOffset: {r: 0, g: 0, b: 0, a: 0}
  m_BuildTextureStacks: []
--- !u!114 &3286349241373283790
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: da692e001514ec24dbc4cca1949ff7e8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 11
