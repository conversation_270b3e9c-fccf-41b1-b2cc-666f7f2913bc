# 第三人称摄像机设置说明（CameraRig版本）

## 概述
我已经为您创建了一个完整的第三人称摄像机系统，专门适配您现有的UserMarker/CameraRig结构。现在您可以在第一人称和第三人称视角之间自由切换来观察物体的移动。

## 新增的脚本文件

### 1. CameraRigThirdPerson.cs
- 专为CameraRig结构设计的第三人称摄像机控制脚本
- 提供平滑的摄像机跟随
- 支持鼠标控制摄像机角度和距离
- 与现有的CameraRig结构完美兼容

### 2. CameraManager.cs（已更新）
- 摄像机切换管理脚本
- 处理第一人称和第三人称视角的切换
- 专门适配CameraRig结构
- 自动查找UserMarker和CameraRig

### 3. ControlsUI.cs
- 控制说明UI脚本
- 在屏幕上显示操作说明

## Unity中的设置步骤

### 方法一：自动设置（推荐）

1. **添加设置助手脚本**
   - 在Hierarchy中创建一个空的GameObject
   - 重命名为 "CameraSetupHelper"
   - 将 `CameraSetupHelper.cs` 脚本添加到这个GameObject上

2. **一键自动设置**
   - 在Inspector中点击 "自动设置第三人称摄像机系统" 按钮
   - 脚本会自动完成所有设置工作

### 方法二：手动设置

### 步骤1：在CameraRig中创建第三人称摄像机
1. 在Hierarchy中找到 UserMarker → CameraRig
2. 右键CameraRig → Create → Camera
3. 重命名新摄像机为 "ThirdPersonCamera"
4. 将 `CameraRigThirdPerson.cs` 脚本添加到这个新摄像机上

### 步骤2：设置摄像机管理器
1. 在UserMarker上添加 `CameraManager.cs` 脚本
2. 在Inspector中设置以下参数：
   - First Person Camera: 拖入CameraRig中的Main Camera
   - Third Person Camera: 拖入刚创建的ThirdPersonCamera
   - Camera Rig: 拖入CameraRig
   - Player Transform: 拖入UserMarker（脚本会自动检测）

### 步骤3：添加控制说明UI
1. 在Hierarchy中创建一个空的GameObject
2. 重命名为 "ControlsUI"
3. 将 `ControlsUI.cs` 脚本添加到这个GameObject上

## 层次结构示例
设置完成后，您的Hierarchy应该看起来像这样：
```
UserMarker (带有ArrowMovement和CameraManager脚本)
├── CameraRig
│   ├── Main Camera (第一人称摄像机)
│   └── ThirdPersonCamera (带有CameraRigThirdPerson脚本)
└── (其他子物体...)

ControlsUI (带有ControlsUI脚本)
```

### 步骤4：调整摄像机参数

#### CameraManager设置（在UserMarker上）
- **Use Original First Person Setup**: 是否使用原有的第一人称设置（默认：开启）
  - 开启：保持您原有的Main Camera设置和行为
  - 关闭：使用脚本控制第一人称摄像机位置

#### CameraRigThirdPerson设置（在第三人称摄像机上）
- **Use Current Position As Offset**: 使用当前摄像机位置作为偏移（默认：开启）
  - 开启：摄像机会根据您在Scene视图中调整的位置来设置跟随偏移
  - 关闭：使用下面的Offset参数
- **Offset**: 摄像机相对于目标的位置偏移（仅在上面选项关闭时使用）
- **Smooth Follow**: 是否平滑跟随（默认：开启）
- **Smooth Speed**: 平滑跟随速度（默认：5）
- **Look At Target**: 是否始终看向目标（默认：开启）
- **Look At Offset**: 看向目标的偏移（默认：0,0,0）

## 如何调整第三人称摄像机位置

### 方法一：在Scene视图中直接调整（推荐）
1. 在Hierarchy中选择ThirdPersonCamera
2. 在Scene视图中直接拖拽摄像机到您想要的位置
3. 确保CameraRigThirdPerson脚本中的"Use Current Position As Offset"是开启的
4. 运行游戏，摄像机会根据您设置的位置进行跟随

### 方法二：通过Inspector调整
1. 关闭"Use Current Position As Offset"选项
2. 手动设置"Offset"参数的X、Y、Z值
3. X轴：左右位置，Y轴：上下位置，Z轴：前后位置

## 控制说明

### 移动控制
- W/S 或 ↑/↓ - 前后移动
- A/D 或 ←/→ - 左右旋转

### 摄像机控制
- **C键** - 在第一人称和第三人称视角之间切换
- **H键** - 隐藏/显示控制说明

## 功能特点

### 第一人称视角
- 摄像机跟随物体移动和旋转
- 提供沉浸式的驾驶体验

### 第三人称视角
- 可以从外部观察物体的移动
- 固定偏移位置跟随
- 可自定义摄像机位置和朝向
- 简单直观的设置

### 智能切换
- 一键切换视角
- 自动管理摄像机状态
- 切换时重置第三人称摄像机位置

## 自定义选项

### 修改切换按键
在CameraManager脚本中修改 `switchCameraKey` 变量

### 调整第一人称摄像机位置
在CameraManager脚本中修改 `firstPersonOffset` 变量

### 修改第三人称摄像机行为
在CameraRigThirdPerson脚本中调整各种参数：
- 位置偏移（offset）
- 平滑跟随设置（smoothFollow, smoothSpeed）
- 朝向设置（lookAtTarget, lookAtOffset）

## 故障排除

### 如果第一人称摄像机行为异常
1. 确保CameraManager中的"Use Original First Person Setup"选项是开启的
2. 检查您原有的Main Camera设置是否正确
3. 如果需要脚本控制第一人称摄像机，可以关闭"Use Original First Person Setup"

### 如果摄像机不跟随目标
1. 确保CameraManager中的Player Transform已正确设置为UserMarker
2. 确保UserMarker有ArrowMovement脚本
3. 检查CameraRig是否正确分配

### 如果切换不工作
1. 检查CameraManager是否正确分配了两个摄像机
2. 确保两个摄像机都在CameraRig中
3. 确保第三人称摄像机有CameraRigThirdPerson脚本

### 如果第三人称摄像机行为异常
1. 检查CameraRigThirdPerson脚本的Target是否正确设置为UserMarker
2. 调整Offset参数来设置摄像机位置
3. 确保摄像机在CameraRig结构中
